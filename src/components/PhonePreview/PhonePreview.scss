.phone-preview {
  position: relative;
  background-image: url('https://img.alicdn.com/imgextra/i4/155168396/O1CN01sKaDbD2BtQ8T94D55_!!155168396.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

.phone-preview-bg {
  width: 100%;
}

.phone-preview-content {
  position: absolute;
  top: 2.9%;
  left: 7.6%;
  overflow: scroll;

  // 隐藏滚动条但保留滚动功能
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE/Edge

  &::-webkit-scrollbar {
    display: none; // Chrome/Safari/Opera
  }
}
