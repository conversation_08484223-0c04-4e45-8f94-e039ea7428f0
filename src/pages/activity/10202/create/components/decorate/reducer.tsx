import {
  ThemeState,
  ThemeAction,
} from './type';

// 原始默认主题状态，保持不变
const originalDefaultThemeState: ThemeState = {
  kv: 'https://img10.360buyimg.com/imgzone/jfs/t1/334488/37/1735/1017826/68a67cacF1e188d04/d4fdf916a19c2859.png',
  actBg: '',
  actBgColor: '#fff',
  ruleBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/326300/3/8545/1469/68a67e79F3a666a63/b71fec93f17038ef.png',
  ruleBtnColor: '#cc0607',
  userInfoBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/325526/3/8539/37357/68a67f1eFcbc44f81/cb04e04ed87c87e8.png',
  userInfoColor: '#fff',
  step1Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/327070/20/8531/50193/68a68089F64079811/70a35be8b523a704.png',
  step2Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/332675/27/1748/56186/68a68213Fcf06271e/02a9071739ac4936.png',
  step2ItemBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/325281/14/8530/24755/68a686c6F8d083409/a35c182ef0e69d32.png',
  step3Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/326617/10/8611/69979/68a68235F8675644e/e07a9c8b235bf53d.png',
  getDemoPrizeBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/323468/16/8676/1811/68a6828aF116f91e5/c95bf98570fd83cf.png',
  ruleImage: 'https://img10.360buyimg.com/imgzone/jfs/t1/330455/9/1768/222855/68a682d5Fce568322/6edb503a02d9bce1.png',
  bottomToTop: 'https://img10.360buyimg.com/imgzone/jfs/t1/321545/26/23362/6508/68a682aaFae825fa3/29f890119209c3a2.png',
};

// 导出的默认主题状态，用于重置
export let defaultThemeState: ThemeState = {
  ...originalDefaultThemeState,
};

// 移除updateDefaultTheme函数，因为它会破坏重置功能

export const themeState: ThemeState = {
  ...defaultThemeState,
};
// 主题Reducer
export function themeReducer(state: ThemeState, action: ThemeAction): ThemeState {
  switch (action.type) {
    case 'UPDATE_MODULE': {
      const processedPayload = { ...action.payload };

      // 遍历payload中的每个属性
      Object.keys(processedPayload).forEach((key) => {
        const defaultValue = originalDefaultThemeState[key];
        switch (processedPayload[key]) {
          // 重置为默认值
          case -1:
            processedPayload[key] = defaultValue;
            break;
          default:
            break;
        }
      });
      return {
        ...state,
        ...processedPayload,
      };
    }
    case 'INIT_MODULE': {
      return {
        ...state,
        ...action.payload,
      };
    }
    default:
      return state;
  }
}