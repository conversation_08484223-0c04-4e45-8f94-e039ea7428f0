
export const usePrizeSectors = (prize: any[]) => {
  const prizeList = prize && prize.length > 0 ? prize : Array(8).fill({
    lotteryName: '',
    showImage: '',
    prizeType: 'THANKS',
  });

  const generatePrizeSectors = (): any[] => {
    const sectors: any[] = [];
    const angleStep = 360 / 8;

    for (let i = 0; i < 8; i++) {
      const startAngle = i * angleStep + 22.5;
      const prize = prizeList[i] || { lotteryName: '', showImage: '', prizeType: 'THANKS' };

      sectors.push({
        id: i,
        prize,
        style: {
          transform: `rotate(${startAngle}deg)`,
        },
      });
    }

    return sectors;
  };

  return {
    prizeSectors: generatePrizeSectors(),
  };
};