import styles from '../index.module.scss';
import { CSSProperties } from 'react';

interface PopupProps {
  popup: any;
  visible: boolean;
}

export default function Popup({ popup, visible }: PopupProps) {
  if (!visible) return null;

  return (
    <div
      className={styles.popup}
      style={{
        '--popupBtnBg': popup.popupBtnBg,
        '--popupBtnText': popup.popupBtnText,
        '--popupFloatTitle': popup.floatTitle,
        '--popupFloatConentTitle': popup.floatConentTitle,
        '--popupFloatConentText': popup.floatConentText,
        '--popupTaskGuideBtnBg': popup.taskGuideBtnBg,
        '--popupTaskGuideText': popup.taskGuideText,
        '--popupAddressBg': popup.addressBg,
        '--popupAddressText': popup.addressText,
      } as CSSProperties}
    >
      <img className={styles.popupBg} src={popup.popupBg} alt="" />
      <div className={styles.popupInfo}>
        <div className={styles.popupInfoTitle}>很抱歉</div>
        <div className={styles.popupInfoTitle}>您暂不满足参与条件</div>
        <div className={styles.popupInfoDesc}>详情见活动规则</div>
        <div className={styles.popupBtn}>我知道了</div>
      </div>
    </div>
  );
}