import { CSSProperties } from 'react';


// 处理图片路径：转换为完整URL格式
const normalizeImageProp = (path?: string) => {
  return path ? `url("${path.replace(/^url\(['"]?|['"]?\)$/g, '')}")` : undefined;
};

export const createTurnTableMainStyles = (main: any) => ({
  '--ruleBtnColor': main.ruleBtnColor,
  '--ruleBtn': normalizeImageProp(main.ruleBtn),
  '--actBgColor': main.actBgColor,
  '--userInfoBg': normalizeImageProp(main.userInfoBg),
  '--userInfoColor': main.userInfoColor,
  '--step1Bg': normalizeImageProp(main.step1Bg),
  '--getDemoPrizeBtn': normalizeImageProp(main.getDemoPrizeBtn),
  '--step2Bg': normalizeImageProp(main.step2Bg),
  '--step2ItemBg': normalizeImageProp(main.step2ItemBg),
  '--step3Bg': normalizeImageProp(main.step3Bg),
  '--checkedSkuLine': main.checkedSkuLine,
  '--unCheckedSkuLine': main.unCheckedSkuLine,
  '--ruleImage': normalizeImageProp(main.ruleImage),
  '--bottomToTop': normalizeImageProp(main.bottomToTop),
  background: main.actBgColor,
} as CSSProperties);

export const scrollToTop = () => {
  const phonePreviewContainer = document.querySelector('.phone-preview-content');
  if (phonePreviewContainer) {
    phonePreviewContainer.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
};