import PhonePreview from '@/components/PhonePreview/PhonePreview';
import styles from './index.module.scss';
import { createTurnTableMainStyles } from './utils/styleUtils';

export default function TurnTablePreview(props) {
  const { tState, state } = props;
  const main = tState;

  return (
    <PhonePreview
      width={290}
    >
      <div style={{ position: 'relative' }}>
        <div
          style={createTurnTableMainStyles(main)}
          className={styles.main}
        >
          <img className={styles.kv} src={main.kv} alt="" />
          <div className={styles.btns}>
            <div>活动规则</div>
            <div>我的订单</div>
            <div>兑换记录</div>
          </div>

          <div className={styles.userInfoBg}>
            <div className={styles.userInfo}>
              <div>XXXXXX用户，您符合转段福利申领资格！</div>
              <div>请点击下方参与活动</div>
            </div>
          </div>

          <div className={styles.step1Bg}>
            <div>XXXXXXXX</div>
            <img src="https://img10.360buyimg.com/imgzone/jfs/t1/310039/3/17886/40981/6879b1a0Fc1e18cab/58c890888a115358.png" alt="" className={styles.tabContainer} />
          </div>

          <img src={main.step2Bg} alt="" className={styles.step2Bg} />
          <div className={styles.step3Bg} />
          <div className={styles.bottomToTop} />
        </div>
      </div>
    </PhonePreview>
  );
}