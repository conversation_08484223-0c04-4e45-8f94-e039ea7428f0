// export interface ThemeState {
//   current: 'main';
//   main: MainState;
// }

export interface ThemeState {
  kv: string;
  actBg: string;
  actBgColor: string;
  ruleBtn: string;
  ruleBtnColor: string;
  userInfoBg: string;
  userInfoColor: string;
  step1Bg: string;
  step2Bg: string;
  step2ItemBg: string;
  step3Bg: string;
  getDemoPrizeBtn: string;
  ruleImage: string;
  bottomToTop: string;
}

type ThemeActionType = 'UPDATE_MODULE' | 'INIT_MODULE';

// 动作类型定义
export type UpdateModuleAction = {
  type: ThemeActionType;
  payload?: Partial<ThemeState>;
};

export type ThemeAction = UpdateModuleAction;