import { Box, Form, Input } from '@alifd/next';
import { useShare } from './hook';
import ImgUpload from '@/components/ImgUpload';

// 默认分享图片
const DEFAULT_SHARE_IMAGE = 'https://img10.360buyimg.com/imgzone/jfs/t1/325989/31/8584/75841/68a68cf5F627a6f6e/09650007194c6cd5.png';

export default function TurnTableShare() {
  const { share, errors, updateShare } = useShare();

  const { shareContent, shareTitle, mpImg } = share;

  const hasTitleError = errors.some(err => err.includes('分享标题'));
  const hasContentError = errors.some(err => err.includes('分享内容'));
  const hasImgError = errors.some(err => err.includes('分享图片'));

  return (
    <Box margin={[16, 0, 0, 0]}>
      <Form.Item
        label={'分享标题'}
        required
        validateState={hasTitleError ? 'error' : undefined}
        help={hasTitleError ? errors.find(err => err.includes('分享标题')) : undefined}
      >
        <Input
          value={shareTitle}
          style={{ width: 400 }}
          onChange={(value: string) => updateShare({ shareTitle: value })}
          maxLength={13}
          showLimitHint
        />
      </Form.Item>

      <Form.Item
        label={'分享内容'}
        required
        validateState={hasContentError ? 'error' : undefined}
        help={hasContentError ? errors.find(err => err.includes('分享内容')) : undefined}
      >
        <Input
          value={shareContent}
          style={{ width: 400 }}
          onChange={(value: string) => updateShare({ shareContent: value })}
          maxLength={15}
          showLimitHint
        />
      </Form.Item>
      <ImgUpload
        label="分享图"
        required
        validateState={hasImgError ? 'error' : undefined}
        help={hasImgError ? errors.find(err => err.includes('分享图片')) : undefined}
        img={{
          value: mpImg,
          width: 512,
          height: 512,
        }}
        onSuccess={(url: string) => updateShare({ mpImg: url })}
        onReset={() => updateShare({ mpImg: DEFAULT_SHARE_IMAGE })}
      />
    </Box>
  );
}