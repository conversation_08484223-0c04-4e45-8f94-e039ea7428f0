import { Box, Button, Form } from '@alifd/next';
import Base from './base';
import { useActivity } from '../../reducer';
import { ModuleType } from '@/pages/activity/10202/create/type';
import { validateBaseInfo } from './base/validator';
import { showErrorMessageDialog } from '@/utils';
import { validateThreshold } from './threshold/validator';
import Threshold from './threshold';
import Order from './order';
import { validateOrder } from './order/validator';
import DemoSkuList from './demoSkuList';
import { validateDemoSkuList } from './demoSkuList/validator';
import Repurchase from './repurchase';
import { validateRepurchase } from './repurchase/validator';
import { ACTIVITY_STATUS } from '@/utils/constant';

// 定义一个统一的验证器接口
interface Validator {
  module: ModuleType;
  getErrors: () => string[];
}

export default function Setting({ goNextStep }) {
  // 使用根级别的状态
  const { state, dispatch } = useActivity();
  const { operationType, activityStatus } = state.extra;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const notStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = (isEdit || isView) && notStart;

  // 创建验证器列表
  const validators: Validator[] = [
    { module: ModuleType.BASE, getErrors: () => validateBaseInfo(state) },
    { module: ModuleType.THRESHOLD, getErrors: () => validateThreshold(state) },
    { module: ModuleType.ORDER, getErrors: () => validateOrder(state) },
    { module: ModuleType.DEMO_SKU_LIST, getErrors: () => validateDemoSkuList(state) },
    { module: ModuleType.REPURCHASE, getErrors: () => validateRepurchase(state) },

    // { module: ModuleType.REPURCHASE_PRIZE_LIST, getErrors: () => validateRepurchasePrizeList(state) },
    // { module: ModuleType.REPURCHASE_SKU_LIST, getErrors: () => validateRepurchaseSkuList(state) },
  ];

  // 统一的验证函数，返回验证结果(是否通过)
  const validateModules = () => {
    let isValid = true;
    const allErrors: string[] = [];
    // 遍历所有验证器获取错误状态
    validators.forEach((validator) => {
      const errors = validator.getErrors();
      dispatch({
        type: 'VALIDATE_MODULE',
        module: validator.module,
        payload: errors,
      });
      allErrors.push(...errors);
      // 如果有错误，则验证不通过
      if (errors.length > 0) {
        isValid = false;
      }
    });
    if (!isValid) {
      showErrorMessageDialog(allErrors);
    }
    return isValid;
  };

  const handleSubmit = () => {
      // todo：解开
    // // 验证所有模块
    // const isValid = validateModules();
    // // 如果验证通过，进入下一步
    // console.log(state, '复购活动数据');
    // isValid && goNextStep();
      goNextStep();
  };

  return (
    <Form disabled={needDisable} className="activity-setting">
      <Base />
      <Threshold />
      <Order />
      <DemoSkuList />
      <Repurchase />
      {/* <RepurchasePrizeList /> */}
      {/* <RepurchaseSkuList /> */}
      <Box
        direction="row"
        justify="center"
        style={{
          position: 'fixed',
          bottom: 22,
          left: '50%',
          transform: 'translateX(calc(-50% + 110px))',
        }}
      >
        <Button style={{ width: 150 }} type="primary" onClick={handleSubmit}>
          下一步：规则设置
        </Button>
      </Box>
    </Form>
  );
}